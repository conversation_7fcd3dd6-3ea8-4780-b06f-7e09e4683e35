<!DOCTYPE html>
<html lang="en">
<?php require_once 'database/secure_conn.php'; ?>

<?php
// Handle form submission with maximum security
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // CSRF Protection (add this token to your form)
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'])) {
            throw new Exception("Invalid request. Please try again.");
        }
        
        // Define validation rules
        $validation_rules = [
            'name' => [
                'required' => true,
                'max_length' => 100
            ],
            'username' => [
                'required' => true,
                'type' => 'username',
                'min_length' => 3,
                'max_length' => 50
            ],
            'email' => [
                'required' => true,
                'type' => 'email',
                'max_length' => 100
            ],
            'phone' => [
                'required' => false,
                'type' => 'phone',
                'max_length' => 20
            ],
            'password' => [
                'required' => true,
                'min_length' => 6,
                'max_length' => 255
            ],
            'user_type' => [
                'required' => true,
                'allowed' => ['Administrator', 'Manager', 'Staff', 'Customer', 'Vendor']
            ],
            'address' => [
                'required' => false,
                'max_length' => 500
            ]
        ];
        
        // Validate input
        $validation = validateInput($_POST, $validation_rules);
        
        if (!$validation['valid']) {
            $message = implode(', ', $validation['errors']);
            $messageType = 'error';
        } else {
            $data = $validation['data'];
            
            // Check if username already exists
            if (recordExists('users', 'username', $data['username'])) {
                $message = 'Username already exists!';
                $messageType = 'error';
            }
            // Check if email already exists
            elseif (recordExists('users', 'email', $data['email'])) {
                $message = 'Email already exists!';
                $messageType = 'error';
            } else {
                // Hash password securely
                $hashed_password = password_hash($data['password'], PASSWORD_ARGON2ID, [
                    'memory_cost' => 65536, // 64 MB
                    'time_cost' => 4,       // 4 iterations
                    'threads' => 3          // 3 threads
                ]);
                
                // Insert user with prepared statement
                $sql = "INSERT INTO users (name, username, email, phone, password, user_type, address, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
                
                $params = [
                    $data['name'],
                    $data['username'],
                    $data['email'],
                    $data['phone'],
                    $hashed_password,
                    $data['user_type'],
                    $data['address']
                ];
                
                $user_id = insertAndGetId($sql, $params);
                
                if ($user_id) {
                    $message = 'User added successfully!';
                    $messageType = 'success';
                    
                    // Log successful user creation (optional)
                    error_log("New user created: ID $user_id, Username: {$data['username']}");
                } else {
                    $message = 'Failed to create user. Please try again.';
                    $messageType = 'error';
                }
            }
        }
        
    } catch (Exception $e) {
        error_log("User creation error: " . $e->getMessage());
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Generate CSRF token for form security
session_start();
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>

<?php include("include/header.php") ?>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include("include/sidebar.php") ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <?php include("include/top_navbar.php") ?>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Manage system users and their permissions</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <button id="addUserBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-user-plus mr-2"></i> Add User
                        </button>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if (!empty($message)): ?>
                <div class="mb-6">
                    <div class="<?php echo $messageType == 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold"><?php echo $messageType == 'success' ? 'Success!' : 'Error!'; ?></strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-<?php echo $messageType == 'success' ? 'green' : 'red'; ?>-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.style.display='none';">
                                <title>Close</title>
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- User Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <?php
                    // Get real user statistics from database
                    try {
                        $total_users = fetchOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0;
                        $active_users = fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type != 'Inactive'")['count'] ?? 0;
                        $admin_users = fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'Administrator'")['count'] ?? 0;
                        $customer_users = fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'Customer'")['count'] ?? 0;
                    } catch (Exception $e) {
                        $total_users = $active_users = $admin_users = $customer_users = 0;
                    }
                    ?>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Users</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($total_users); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Active Users</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($active_users); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Administrators</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($admin_users); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-user-friends"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Customers</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($customer_users); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">All Users</h3>
                        <div class="flex items-center space-x-2">
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-file-export"></i>
                            </button>
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php
                                try {
                                    $users = fetchAll("SELECT id, name, username, email, phone, user_type, created_at FROM users ORDER BY created_at DESC LIMIT 10");
                                    foreach ($users as $user):
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                    <span class="text-indigo-600 font-medium"><?php echo strtoupper(substr($user['name'], 0, 2)); ?></span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></div>
                                                <div class="text-sm text-gray-500">@<?php echo htmlspecialchars($user['username']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($user['email']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($user['phone'] ?: 'N/A'); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $role_colors = [
                                            'Administrator' => 'bg-purple-100 text-purple-800',
                                            'Manager' => 'bg-blue-100 text-blue-800',
                                            'Staff' => 'bg-yellow-100 text-yellow-800',
                                            'Customer' => 'bg-green-100 text-green-800',
                                            'Vendor' => 'bg-gray-100 text-gray-800'
                                        ];
                                        $color_class = $role_colors[$user['user_type']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $color_class; ?>">
                                            <?php echo htmlspecialchars($user['user_type']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3"><i class="fas fa-edit"></i></a>
                                        <a href="#" class="text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></a>
                                    </td>
                                </tr>
                                <?php 
                                    endforeach;
                                } catch (Exception $e) {
                                    echo '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No users found</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Secure Add User Modal -->
    <div id="addUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex items-center justify-between pb-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">Add New User</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="mt-4">
                    <form id="addUserForm" method="POST" action="">
                        <!-- CSRF Token for security -->
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                                <input type="text" id="name" name="name" required maxlength="100"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Enter full name">
                            </div>

                            <!-- Username -->
                            <div>
                                <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username *</label>
                                <input type="text" id="username" name="username" required maxlength="50" pattern="[a-zA-Z0-9_]{3,20}"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Enter username (3-20 chars, letters, numbers, underscore only)">
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                                <input type="email" id="email" name="email" required maxlength="100"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Enter email address">
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                <input type="tel" id="phone" name="phone" maxlength="20"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Enter phone number">
                            </div>

                            <!-- Password -->
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                                <input type="password" id="password" name="password" required minlength="6" maxlength="255"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Enter password (min 6 characters)">
                                <div class="mt-1 text-xs text-gray-500">
                                    Password strength: <span id="passwordStrength" class="font-medium">Weak</span>
                                </div>
                            </div>

                            <!-- User Type -->
                            <div>
                                <label for="user_type" class="block text-sm font-medium text-gray-700 mb-1">User Type *</label>
                                <select id="user_type" name="user_type" required
                                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="">Select user type</option>
                                    <option value="Administrator">Administrator</option>
                                    <option value="Manager">Manager</option>
                                    <option value="Staff">Staff</option>
                                    <option value="Customer">Customer</option>
                                    <option value="Vendor">Vendor</option>
                                </select>
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mt-4">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea id="address" name="address" rows="3" maxlength="500"
                                      class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                      placeholder="Enter address"></textarea>
                        </div>

                        <!-- Modal Footer -->
                        <div class="flex items-center justify-end pt-4 border-t mt-6 space-x-3">
                            <button type="button" id="cancelBtn"
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Cancel
                            </button>
                            <button type="submit" id="submitBtn"
                                    class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-save mr-2"></i>Add User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced secure modal functionality
        const addUserBtn = document.getElementById('addUserBtn');
        const addUserModal = document.getElementById('addUserModal');
        const closeModal = document.getElementById('closeModal');
        const cancelBtn = document.getElementById('cancelBtn');
        const addUserForm = document.getElementById('addUserForm');
        const submitBtn = document.getElementById('submitBtn');

        // Show modal
        addUserBtn.addEventListener('click', function() {
            addUserModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            document.getElementById('name').focus(); // Focus first field
        });

        // Hide modal function
        function hideModal() {
            addUserModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
            addUserForm.reset();
            clearValidationErrors();
        }

        // Clear validation errors
        function clearValidationErrors() {
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
            });
            document.querySelectorAll('.text-red-500').forEach(el => {
                el.remove();
            });
        }

        // Close modal events
        closeModal.addEventListener('click', hideModal);
        cancelBtn.addEventListener('click', hideModal);

        // Close modal when clicking outside
        addUserModal.addEventListener('click', function(e) {
            if (e.target === addUserModal) {
                hideModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !addUserModal.classList.contains('hidden')) {
                hideModal();
            }
        });

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthEl = document.getElementById('passwordStrength');
            let strength = 'Weak';
            let color = 'text-red-500';

            if (password.length >= 8) {
                const hasUpper = /[A-Z]/.test(password);
                const hasLower = /[a-z]/.test(password);
                const hasNumber = /\d/.test(password);
                const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

                const score = [hasUpper, hasLower, hasNumber, hasSpecial].filter(Boolean).length;

                if (score >= 3) {
                    strength = 'Strong';
                    color = 'text-green-500';
                } else if (score >= 2) {
                    strength = 'Medium';
                    color = 'text-yellow-500';
                }
            }

            strengthEl.textContent = strength;
            strengthEl.className = `font-medium ${color}`;
        });

        // Enhanced form validation
        addUserForm.addEventListener('submit', function(e) {
            let isValid = true;
            clearValidationErrors();

            // Validate required fields
            const requiredFields = ['name', 'username', 'email', 'password', 'user_type'];
            requiredFields.forEach(function(fieldName) {
                const field = document.getElementById(fieldName);
                if (!field.value.trim()) {
                    showFieldError(field, `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
                    isValid = false;
                }
            });

            // Username validation
            const username = document.getElementById('username');
            if (username.value && !/^[a-zA-Z0-9_]{3,20}$/.test(username.value)) {
                showFieldError(username, 'Username must be 3-20 characters, letters, numbers, and underscores only');
                isValid = false;
            }

            // Email validation
            const email = document.getElementById('email');
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (email.value && !emailRegex.test(email.value)) {
                showFieldError(email, 'Please enter a valid email address');
                isValid = false;
            }

            // Password validation
            const password = document.getElementById('password');
            if (password.value && password.value.length < 6) {
                showFieldError(password, 'Password must be at least 6 characters long');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                return false;
            }

            // Disable submit button to prevent double submission
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding User...';
        });

        // Show field error
        function showFieldError(field, message) {
            field.classList.add('border-red-500');
            const errorEl = document.createElement('div');
            errorEl.className = 'text-red-500 text-xs mt-1';
            errorEl.textContent = message;
            field.parentNode.appendChild(errorEl);
        }

        // Real-time validation feedback
        document.querySelectorAll('#addUserForm input, #addUserForm select, #addUserForm textarea').forEach(function(field) {
            field.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('border-red-500');
                } else {
                    this.classList.remove('border-red-500');
                }
            });

            field.addEventListener('input', function() {
                if (this.classList.contains('border-red-500') && this.value.trim()) {
                    this.classList.remove('border-red-500');
                    // Remove error message
                    const errorEl = this.parentNode.querySelector('.text-red-500');
                    if (errorEl) errorEl.remove();
                }
            });
        });
    </script>
    <!-- <script src="js/main.js"></script> -->
</body>
</html>
